import statsmodels.api as sm
import numpy as np
import pandas as pd

# 参数
chromo_num = 200
gene_num = 10  # p_list长度
p_range = [-1, 1]  # 每个基因的取值范围
before_trade_day = 10  # 用前10根K线算p_list
after_trade_day = 5    # 看后5根K线的收益
R2_threshold = 0.5  # R平方阈值, 低于这个阈值的染色体直接淘汰
lowest_score = -10
base_score = 0  


def init_chromo():
	# 初始化第一代染色体
	chromo_list = []
	for _ in range(chromo_num):
		# 均匀分布
		chromo = np.random.uniform(low=p_range[0], high=p_range[1], size=gene_num)
		chromo_list.append(chromo)
	return chromo_list


def get_profit_seq_list(csv_file):
	# 读取csv文件，获取profit序列
	df = pd.read_csv(csv_file)
	profit_seq_list = []
	for idx in range(before_trade_day, len(df)-after_trade_day):
		profit_seq = []
		# 计算前before_trade_day个交易日的收益率序列
		for i in range(before_trade_day):
			before_row = idx - before_trade_day + i
			if before_row >= 0 and before_row + 1 < len(df):
				profit = (df.iloc[before_row + 1]['close'] / df.iloc[before_row]['close']) - 1
				profit_seq.append(profit)

		# 只有当收集到完整的序列时才添加
		if len(profit_seq) == before_trade_day:
			profit_seq_list.append(profit_seq)

	return profit_seq_list, df


def evaluate_profit(chromo_seq, profit_seq_list, df):
	total_score = 0
	valid_predictions = 0

	for idx, profit_seq in enumerate(profit_seq_list):
		# 前面的是真实值, 后面的是预测值
		model = sm.OLS(profit_seq, chromo_seq).fit()
		if model.rsquared < R2_threshold:
			continue

		# 计算未来收益率：从当前位置开始的after_trade_day天后的收益率
		current_idx = before_trade_day + idx  # 当前在原始df中的位置
		future_idx = current_idx + after_trade_day  # 未来位置

		if future_idx > len(df):
			break
		# 计算未来收益率：(未来价格 / 当前价格) - 1
		future_profit = (df.iloc[future_idx]['close'] / df.iloc[current_idx]['close']) - 1
		# 使用模型预测值作为信号，计算收益
		predicted_signal = model.predict(chromo_seq)[0]  # 获取预测值
		# 根据预测信号和实际收益计算得分
		if predicted_signal > 0 and future_profit > 0:
			# 预测上涨且实际上涨
			score = future_profit * 100  # 转换为百分比
		elif predicted_signal < 0 and future_profit < 0:
			# 预测下跌且实际下跌（做空获利）
			score = abs(future_profit) * 100
		else:
			# 预测错误
			score = lowest_score
		total_score += score
		valid_predictions += 1

	# 返回平均得分
	if valid_predictions > 0:
		return total_score / valid_predictions
	else:
		return lowest_score


def main():
	# 初始化染色体
	chromo_seq_list = init_chromo()
	print(f'初始化了 {len(chromo_seq_list)} 个染色体')

	# 获取收益率序列和原始数据
	profit_seq_list, df = get_profit_seq_list('k_data/WLD_USDT_USDT_train.csv')
	print(f"获取了 {len(profit_seq_list)} 个收益率序列")

	# 评估每个染色体的适应度
	fitness_scores = []
	for i, chromo_seq in enumerate(chromo_seq_list):
		score = evaluate_profit(chromo_seq, profit_seq_list, df)
		fitness_scores.append(score)
		if i % 50 == 0:  # 每50个染色体打印一次进度
			print(f"已评估 {i+1}/{len(chromo_seq_list)} 个染色体")

	# 显示结果
	fitness_scores = np.array(fitness_scores)
	print(f"\n=== 评估结果 ===")
	print(f"最高得分: {fitness_scores.max():.4f}")
	print(f"最低得分: {fitness_scores.min():.4f}")
	print(f"平均得分: {fitness_scores.mean():.4f}")
	print(f"标准差: {fitness_scores.std():.4f}")

	# 找出最优染色体
	best_idx = np.argmax(fitness_scores)
	best_chromo = chromo_seq_list[best_idx]
	print(f"\n最优染色体 (索引 {best_idx}):")
	print(f"得分: {fitness_scores[best_idx]:.4f}")
	print(f"基因: {best_chromo}")


if __name__ == "__main__":
	main()
