import statsmodels.api as sm
import numpy as np
import pandas as pd

# 参数
chromo_num = 200
gene_num = 10  # p_list长度
p_range = [-1, 1]  # 每个基因的取值范围
before_trade_day = 10  # 用前10根K线算p_list
after_trade_day = 5    # 看后5根K线的收益
R2_threshold = 0.5  # R平方阈值, 低于这个阈值的染色体直接淘汰
lowest_score = -10
base_score = 0  


def init_chromo():
	# 初始化第一代染色体
	chromo_list = []
	for _ in range(chromo_num):
		# 均匀分布
		chromo = np.random.uniform(low=p_range[0], high=p_range[1], size=gene_num)
		chromo_list.append(chromo)
	return chromo_list


def get_profit_seq_list(csv_file):
	# 读取csv文件，获取profit序列
	df = pd.read_csv(csv_file)
	profit_seq_list = []
	for idx in range(before_trade_day, len(df)-after_trade_day):
		profit_seq = []
		# 比如用前10个, i就要从第9行开始, 因为0-9行共有10个了
		for i in range(before_trade_day-1):
			before_row = idx-before_trade_day+i
			profit = (df[before_row+after_trade_day]['close'] / df[before_row]['close']) - 1
			profit_seq.append(profit)
		profit_seq_list.append(profit_seq)
	return profit_seq_list


def evaluate_profit(chromo_seq, profit_seq_list):
	for profit_seq in profit_seq_list:
		model = sm.OLS(profit_seq, chromo_seq).fit()
		if model.rsquared < R2_threshold:
			continue
	...


def main():
	chromo_seq_list = init_chromo()
	profit_seq_list = get_profit_seq_list('k_data/WLD_USDT_USDT_train.csv')
