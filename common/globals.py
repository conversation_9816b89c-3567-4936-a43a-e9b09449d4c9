"""
全局配置和模型注册表
统一管理所有回测相关的配置和模型注册
"""

import os
import sys
from dataclasses import dataclass
from typing import Dict, Any, List, Tuple
from abc import ABC, abstractmethod
import numpy as np
import pandas as pd

from common.engineer import engineer_features_X

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


class BaseModel(ABC):
    """模型基类接口"""

    @abstractmethod
    def prepare_data(self, csv_file: str, **kwargs) -> Tuple[np.ndarray, np.ndarray, List[str]]:
        """准备数据"""
        pass

    @abstractmethod
    def fit(self, X_train, y_train, **kwargs):
        """训练模型"""
        pass

    @abstractmethod
    def predict(self, X) -> np.ndarray:
        """预测"""
        pass

    @abstractmethod
    def save_model(self, path: str) -> None:
        """保存模型"""
        pass

    @abstractmethod
    def load_model(self, path: str) -> None:
        """加载模型"""
        pass
    

class BaseClassifyModel(BaseModel):
    def prepare_data(self, csv_file: str, balance_classes=True) -> Tuple[np.ndarray, np.ndarray, List[str]]:
        """
        准备训练数据

        Args:
            csv_file: CSV文件路径
            balance_classes: 是否进行类别平衡，默认True

        Returns:
            X: 特征数据 [样本数, 特征数]
            y: 目标标签 [样本数]
            dates: 对应的日期列表
        """
        print(f"加载数据: {csv_file}")

        # 1. 读取数据
        df = pd.read_csv(csv_file)
        df['datetime'] = pd.to_datetime(df['datetime'])
        df = df.sort_values('datetime').reset_index(drop=True)
        print(f"原始数据形状: {df.shape}")

        # 2. 特征工程
        print("开始特征工程...")
        features_df = engineer_features_X(df)
        print(f"特征工程完成，特征数量: {features_df.shape[1]}")

        # 3. 创建目标变量（与trmV2保持一致）
        future_return_1d = df['close'].shift(-1) / df['close'] - 1
        future_return_2d = df['close'].shift(-2) / df['close'] - 1
        future_return_3d = df['close'].shift(-3) / df['close'] - 1
        future_return_avg = (future_return_1d + future_return_2d + future_return_3d) / 3

        # 使用绝对阈值创建标签
        threshold = 0.001
        def assign_label_absolute(return_val):
            if pd.isna(return_val):
                return np.nan
            elif return_val <= -threshold:
                return 0  # 跌
            elif return_val >= threshold:
                return 2  # 涨
            else:
                return 1  # 平

        features_df['target'] = future_return_avg.apply(assign_label_absolute)

        # 4. 清理数据
        features_clean = features_df.dropna()
        print(f"清理后数据形状: {features_clean.shape}")

        # 5. 分离特征和目标
        X_features = features_clean.iloc[:, :-1].values
        y_target = features_clean.iloc[:, -1].values

        # 6. 直接使用清理后的数据（LightGBM不需要标准化）
        X_normalized = X_features
        y_target_valid = y_target
        print(f"数据准备完成，形状: X={X_normalized.shape}, y={y_target_valid.shape}")

        # 7. 获取对应的日期
        valid_dates = df['datetime'].iloc[len(df) - len(features_clean):].tolist()

        # 8. 保存特征名称
        self.feature_names = features_clean.columns[:-1].tolist()

        # 统计类别分布
        unique, counts = np.unique(np.array(y_target_valid), return_counts=True)
        total = len(y_target_valid)
        print("原始目标分布:")
        class_names = ['跌', '平', '涨']
        for label, count in zip(unique, counts):
            pct = count / total * 100
            print(f"  类别{int(label)} ({class_names[int(label)]}): {count} 样本 ({pct:.1f}%)")

        # 类别平衡（可选）
        if balance_classes:
            X_normalized, y_target_valid, valid_dates = self.balance_classes(X_normalized, y_target_valid.astype(int), valid_dates)

        return X_normalized, y_target_valid.astype(int), valid_dates

    def balance_classes(self, X: np.ndarray, y: np.ndarray, dates: List[str]) -> Tuple[np.ndarray, np.ndarray, List[str]]:
        """
        平衡类别数量，以最少的类别数量为准，其他类别随机抽样到相同数量

        Args:
            X: 特征数据
            y: 标签数据
            dates: 日期列表

        Returns:
            平衡后的X, y, dates
        """
        print("\n=== 开始类别平衡 ===")

        # 统计各类别数量
        unique, counts = np.unique(y, return_counts=True)
        class_names = ['跌', '平', '涨']

        print("平衡前类别分布:")
        for label, count in zip(unique, counts):
            pct = count / len(y) * 100
            print(f"  类别{int(label)} ({class_names[int(label)]}): {count} 样本 ({pct:.1f}%)")

        # 找到最少的类别数量
        min_count = np.min(counts)
        print(f"\n以最少类别数量为准: {min_count} 样本")

        # 为每个类别随机抽样
        balanced_indices = []
        np.random.seed(42)  # 设置随机种子保证可重复性

        for class_label in unique:
            # 找到该类别的所有索引
            class_indices = np.where(y == class_label)[0]

            if len(class_indices) > min_count:
                # 如果该类别样本数超过最小值，随机抽样
                sampled_indices = np.random.choice(class_indices, size=min_count, replace=False)
                balanced_indices.extend(sampled_indices)
                print(f"  类别{int(class_label)} ({class_names[int(class_label)]}): 从{len(class_indices)}个样本中抽取{min_count}个")
            else:
                # 如果该类别样本数等于最小值，全部保留
                balanced_indices.extend(class_indices)
                print(f"  类别{int(class_label)} ({class_names[int(class_label)]}): 保留全部{len(class_indices)}个样本")

        # 排序索引以保持时间顺序
        balanced_indices = sorted(balanced_indices)

        # 提取平衡后的数据
        X_balanced = X[balanced_indices]
        y_balanced = y[balanced_indices]
        dates_balanced = [dates[i] for i in balanced_indices]

        # 验证平衡结果
        unique_balanced, counts_balanced = np.unique(y_balanced, return_counts=True)
        print("\n平衡后类别分布:")
        total_balanced = len(y_balanced)
        for label, count in zip(unique_balanced, counts_balanced):
            pct = count / total_balanced * 100
            print(f"  类别{int(label)} ({class_names[int(label)]}): {count} 样本 ({pct:.1f}%)")

        print(f"总样本数: {len(y)} -> {len(y_balanced)}")
        print("=== 类别平衡完成 ===\n")

        return X_balanced, y_balanced, dates_balanced
    ...
    


# 机器学习模型注册表: 2D数据 (batch_size, features)
ML_MODELS: Dict[str, Dict[str, Any]] = {
    'lgbm_classify': {
        'module': 'models.lightgbm.lgbm_classify',
        'class': 'LightGBMStockModel',
        'type': 'classification',
        'data_type': 'ml',
        'default_params': {
            'num_leaves': 31,
            'learning_rate': 0.05,
            'n_estimators': 100,
            'random_state': 42
        }
    },
    'lgbm_regression': {
        'module': 'models.lightgbm.lgbm_regression',
        'class': 'LightGBMStockRegressionModel',
        'type': 'regression',
        'data_type': 'ml',
        'default_params': {
            'num_leaves': 31,
            'learning_rate': 0.05,
            'n_estimators': 100,
            'random_state': 42
        }
    }
}

# 深度学习模型注册表: 3D数据 (batch_size, seq_len, features)
DL_MODELS: Dict[str, Dict[str, Any]] = {
    'transformer_classify': {
        'module': 'models.trm.trmV2',
        'class': 'TradeTransformer',
        'type': 'classification',
        'data_type': 'dl',
        'default_params': {
            'input_dim': None,  # 运行时确定
            'pos_dim': 16,
            'model_dim': 64,
            'nhead': 4,
            'num_layers': 2,
            'num_classes': 3,
            'max_seq_len': 200
        }
    },
    'lstm_classify': {
        'module': 'models.lstm.lstmV8',  # 假设使用最新版本
        'class': 'LSTM',
        'type': 'classification',
        'data_type': 'dl',
        'default_params': {
            'input_dim': None,  # 运行时确定
            'hidden_dim': 64,
            'num_layers': 2,
            'output_dim': 3
        }
    }
}

# 合并所有模型注册表
ALL_MODELS = {**ML_MODELS, **DL_MODELS}


def get_model_info(model_name: str) -> Dict[str, Any]:
    """
    获取模型信息
    
    Args:
        model_name: 模型名称
        
    Returns:
        模型信息字典
        
    Raises:
        ValueError: 如果模型不存在
    """
    if model_name not in ALL_MODELS:
        available_models = list(ALL_MODELS.keys())
        raise ValueError(f"模型 '{model_name}' 不存在。可用模型: {available_models}")
    
    return ALL_MODELS[model_name]


def get_models_by_type(model_type: str) -> Dict[str, Dict[str, Any]]:
    """
    根据类型获取模型列表
    
    Args:
        model_type: 'classification' 或 'regression'
        
    Returns:
        符合类型的模型字典
    """
    return {name: info for name, info in ALL_MODELS.items() 
            if info['type'] == model_type}


def get_models_by_data_type(data_type: str) -> Dict[str, Dict[str, Any]]:
    """
    根据数据类型获取模型列表
    
    Args:
        data_type: 'ml' 或 'dl'
        
    Returns:
        符合数据类型的模型字典
    """
    return {name: info for name, info in ALL_MODELS.items() 
            if info['data_type'] == data_type}


def create_model_instance(model_name: str, **kwargs):
    """
    创建模型实例
    
    Args:
        model_name: 模型名称
        **kwargs: 模型参数
        
    Returns:
        模型实例
    """
    model_info = get_model_info(model_name)
    
    # 动态导入模块
    module_name = model_info['module']
    class_name = model_info['class']
    
    module = __import__(module_name, fromlist=[class_name])
    model_class = getattr(module, class_name)
    
    # 合并默认参数和用户参数
    params = model_info['default_params'].copy()
    params.update(kwargs)
    
    # 创建实例
    return model_class(**params)


# 评估指标配置
CLASSIFICATION_METRICS = ['accuracy', 'precision', 'recall', 'f1', 'confusion_matrix']
REGRESSION_METRICS = ['mse', 'mae', 'rmse', 'r2', 'direction_accuracy']

# 文件路径配置
DEFAULT_PATHS = {
    'data_dir': './k_data',
    'model_dir': './models_saved',
    'results_dir': './results',
    'plots_dir': './plots'
}
